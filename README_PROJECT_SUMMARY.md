# 好宠粮 - 宠物粮食知识共享与交流社区

## 项目概述

**好宠粮**是一个全栈宠物粮食知识共享与交流社区平台，致力于为宠物主人、兽医、宠物营养专家和行业从业者提供权威、科学、实用的宠物粮食知识，帮助宠物主人做出明智的喂养决策。

### 核心价值
- 🎯 **信息透明化**: 建立权威的宠物粮食知识库，解决信息不对称问题
- 🤝 **社区互动**: 提供专业的交流平台，分享真实使用体验
- 📊 **数据驱动**: 多维度评分体系和数据可视化分析
- 🔍 **智能搜索**: 高效的搜索和筛选系统

## 技术架构

### 前端技术栈
- **框架**: React 19 + TypeScript + Vite
- **UI组件**: Ant Design 5.x + Shadcn/UI + Radix UI
- **样式**: Tailwind CSS 4.x + CSS Variables
- **状态管理**: Zustand + React Query (TanStack Query)
- **动画**: Motion/React (替代Framer Motion)
- **表单**: React Hook Form + Zod 验证
- **路由**: React Router 7.x
- **国际化**: i18next + react-i18next
- **图表**: ApexCharts + React ApexCharts
- **编辑器**: React Quill
- **工具链**: Biome (替代ESLint + Prettier)

### 后端技术栈
- **运行时**: Node.js 20 + Express.js + TypeScript
- **数据库**: MongoDB + Mongoose ODM
- **身份认证**: JWT + bcrypt
- **文件上传**: Multer + 静态文件服务
- **邮件服务**: Resend API
- **API设计**: RESTful API + 统一响应格式
- **中间件**: CORS + 请求日志 + 错误处理
- **开发工具**: ts-node + nodemon

### 移动端技术栈
- **框架**: React Native + Expo
- **导航**: Expo Router
- **UI组件**: Tailwind CSS + DaisyUI
- **状态管理**: React Context + Custom Hooks
- **网络请求**: Axios + 自定义 API Client

## 核心功能模块

### 1. 用户系统
- **双端用户体系**: 管理端用户(User) + 前端用户(FontUser)
- **完整用户资料**: 头像、背景图、个人简介、性别、生日、位置等
- **邮箱验证**: 完整的邮箱验证流程
- **权限管理**: 基于角色的权限控制系统

### 2. 产品与品牌管理
- **产品信息**: 详细的营养成分分析、过敏原标识、质量评估
- **品牌管理**: 品牌信息、Logo、官网链接、描述
- **图片管理**: 统一的资源管理策略 (`assets/product_images/`, `assets/brand_logo/`)

### 3. 评论评分系统
- **多维度评分**: 1-10分评分体系
- **长短评论**: 支持简短评价和详细评测
- **嵌套回复**: 支持多层级回复结构
- **互动功能**: 点赞、踩、举报、推荐标记
- **评分汇总**: 自动计算平均评分和评论统计

### 4. 社区论坛
- **帖子系统**: 支持文字、图片(最多9张)的帖子发布
- **多层回复**: 支持主帖、一级回复、二级回复的层级结构
- **互动功能**: 点赞、收藏、回复计数
- **内容审核**: 帖子审核机制和状态管理
- **排序算法**: 支持按时间、热度等多种排序方式

### 5. 私信系统
- **实时消息**: 用户间私信功能
- **多媒体支持**: 文字、图片消息(最多5张)
- **会话管理**: 会话列表、未读消息统计
- **消息状态**: 已读/未读状态跟踪

### 6. 智能搜索系统
- **全文搜索**: 支持产品、品牌、帖子的模糊搜索
- **实时预览**: 搜索时的实时结果预览
- **分类搜索**: 按类型分别搜索和展示结果
- **高级筛选**: 多维度筛选条件(品牌、类型、评分等)
- **搜索统计**: 热门搜索词统计

### 7. 数据统计分析
- **仪表盘**: 用户、内容、互动数据的可视化展示
- **增长分析**: 用户增长率、内容增长趋势
- **热门排行**: 热门品牌、产品排行榜
- **用户行为**: 浏览历史、搜索记录统计

## 技术亮点与难点

### 1. 复杂的数据模型设计
- **多层级回复系统**: 实现了帖子和评论的多层嵌套结构
- **评分汇总机制**: 实时计算和更新产品/品牌的平均评分
- **用户关系管理**: 点赞、收藏、关注等多种用户关系的设计

### 2. 高性能搜索系统
- **MongoDB文本索引**: 利用MongoDB的全文搜索能力
- **搜索结果优化**: 实现了搜索预览和详细搜索的分离
- **多类型聚合搜索**: 同时搜索产品、品牌、帖子并合理分页

### 3. 统一的API设计
- **响应格式标准化**: 统一的ResponseData格式
- **错误处理机制**: 完善的错误捕获和响应
- **分页策略**: 统一的分页参数和响应格式
- **API拦截器**: 请求/响应的统一处理

### 4. 前端架构优化
- **组件化设计**: 高度复用的UI组件库
- **状态管理**: Zustand + React Query的组合使用
- **性能优化**: 懒加载、虚拟滚动、图片优化
- **类型安全**: 完整的TypeScript类型定义

### 5. 移动端适配
- **响应式设计**: 支持多端适配的UI组件
- **原生体验**: React Native实现的移动端应用
- **统一API**: 前端和移动端共享同一套API

### 6. 开发工具链优化
- **现代化工具**: 使用Biome替代ESLint+Prettier
- **动画库升级**: 从Framer Motion迁移到Motion/React
- **构建优化**: Vite构建工具的性能优化

## 项目规模

- **代码行数**: 约15,000+ 行TypeScript/JavaScript代码
- **数据模型**: 10+ 个核心数据模型
- **API接口**: 50+ 个RESTful API端点
- **UI组件**: 100+ 个可复用组件
- **页面数量**: 30+ 个功能页面

## 部署与运维

- **容器化**: Docker支持
- **环境配置**: 开发/生产环境分离
- **静态资源**: 本地文件服务 + CDN策略
- **数据库**: MongoDB集群部署
- **监控日志**: 请求日志中间件

## 项目特色

1. **业务深度**: 深入宠物行业，解决实际痛点
2. **技术广度**: 全栈开发，涵盖Web、移动端、后端
3. **架构合理**: 清晰的分层架构和模块化设计
4. **用户体验**: 注重交互细节和性能优化
5. **可扩展性**: 良好的代码结构，便于功能扩展
6. **现代化**: 使用最新的技术栈和开发工具

这个项目展示了从需求分析、架构设计、技术选型到具体实现的完整开发能力，是一个具有商业价值的全栈项目。
